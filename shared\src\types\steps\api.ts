import { RpaStepBase } from './base';

// API steps (future implementation)
// These are placeholder types for future API integration functionality

export interface ApiCallStep extends RpaStepBase {
  type: 'apiCall';
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  variableName?: string; // Variable name to store response
}

export interface ApiAuthStep extends RpaStepBase {
  type: 'apiAuth';
  authType: 'bearer' | 'basic' | 'apikey';
  credentialId: string;
  variableName?: string; // Variable name to store auth token
}

// Fortnox API steps

/**
 * Fortnox Create Voucher step
 * Uses AI to create voucher rows based on input data and user prompt
 */
export interface FortnoxCreateVoucherStep extends RpaStepBase {
  type: 'fortnoxCreateVoucher';
  description?: string; // Voucher description
  inputVariable: string; // Variable containing input data for AI processing
  aiPrompt: string; // AI prompt describing how to create the voucher
  voucherSeries?: string; // Optional voucher series (defaults to 'A')
  transactionDate?: string; // Optional transaction date (defaults to today)
  variableName?: string; // Variable name to store voucher result (defaults to 'var-fortnox-voucher')
}
